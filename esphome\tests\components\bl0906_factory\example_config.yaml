esphome:
  name: power-monitor
  friendly_name: "Power Monitor"

# 基本系统设置
esp32:
  board: esp32dev
  framework:
    type: arduino

# 启用WiFi连接
wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password
  
  # 启用回退AP模式
  ap:
    ssid: "Power Monitor Fallback"
    password: !secret fallback_password

# 启用API与Home Assistant集成
api:
  encryption:
    key: !secret api_encryption_key

# OTA更新支持
ota:
  password: !secret ota_password

# 日志设置
logger:
  level: INFO
  
# 设置UART串口
uart:
  id: uart_bus
  tx_pin: GPIO17
  rx_pin: GPIO16
  baud_rate: 4800
  data_bits: 8
  parity: NONE
  stop_bits: 1

# BL0906芯片配置
bl0906_factory:
  id: bl0906_chip
  uart_id: uart_bus
  update_interval: 5s
  # 新增校准配置部分
  calibration:
    # 通道1校准参数
    channel_1:
      current_gain: 100       # 电流增益校准值 (CHGN_1)
      current_offset: -50     # 电流偏置校准值 (CHOS_1)
      rms_gain: 120           # 有效值增益校准值 (RMSGN_1)
      rms_offset: -30         # 有效值偏置校准值 (RMSOS_1)
      power_gain: 110         # 功率增益校准值 (WATTGN_1)
      power_offset: -20       # 功率偏置校准值 (WATTOS_1)
    
    # 通道2校准参数
    channel_2:
      current_gain: 95        # 电流增益校准值 (CHGN_2)
      current_offset: -45     # 电流偏置校准值 (CHOS_2)
    
    # 通道3校准参数
    channel_3:
      current_gain: 105       # 电流增益校准值 (CHGN_3)
      current_offset: -55     # 电流偏置校准值 (CHOS_3)
    
    # 通道4校准参数
    channel_4:
      current_gain: 98        # 电流增益校准值 (CHGN_4)
      current_offset: -48     # 电流偏置校准值 (CHOS_4)
    
    # 通道5校准参数
    channel_5:
      current_gain: 102       # 电流增益校准值 (CHGN_5)
      current_offset: -52     # 电流偏置校准值 (CHOS_5)
    
    # 通道6校准参数
    channel_6:
      current_gain: 97        # 电流增益校准值 (CHGN_6)
      current_offset: -47     # 电流偏置校准值 (CHOS_6)
    
    # 电压通道校准参数
    voltage:
      voltage_gain: 100       # 电压增益校准值 (CHGN_V)
      voltage_offset: -25     # 电压偏置校准值 (CHOS_V)

# 传感器配置
sensor:
  # 电压传感器
  - platform: bl0906_factory
    bl0906_factory_id: bl0906_chip
    voltage:
      name: "Line Voltage"
      unit_of_measurement: V
      accuracy_decimals: 1
    
    # 电流传感器
    current_1:
      name: "Channel 1 Current"
      unit_of_measurement: A
      accuracy_decimals: 3
    current_2:
      name: "Channel 2 Current"
      unit_of_measurement: A
      accuracy_decimals: 3
    current_3:
      name: "Channel 3 Current"
      unit_of_measurement: A
      accuracy_decimals: 3
    current_4:
      name: "Channel 4 Current"
      unit_of_measurement: A
      accuracy_decimals: 3
    current_5:
      name: "Channel 5 Current"
      unit_of_measurement: A
      accuracy_decimals: 3
    current_6:
      name: "Channel 6 Current"
      unit_of_measurement: A
      accuracy_decimals: 3
    
    # 功率传感器
    power_1:
      name: "Channel 1 Power"
      unit_of_measurement: W
      accuracy_decimals: 1
    power_2:
      name: "Channel 2 Power"
      unit_of_measurement: W
      accuracy_decimals: 1
    power_3:
      name: "Channel 3 Power"
      unit_of_measurement: W
      accuracy_decimals: 1
    power_4:
      name: "Channel 4 Power"
      unit_of_measurement: W
      accuracy_decimals: 1
    power_5:
      name: "Channel 5 Power"
      unit_of_measurement: W
      accuracy_decimals: 1
    power_6:
      name: "Channel 6 Power"
      unit_of_measurement: W
      accuracy_decimals: 1
    
    # 总功率
    power_sum:
      name: "Total Power"
      unit_of_measurement: W
      accuracy_decimals: 1
    
    # 电能计量
    energy_1:
      name: "Channel 1 Energy"
      unit_of_measurement: kWh
      accuracy_decimals: 3
    energy_2:
      name: "Channel 2 Energy"
      unit_of_measurement: kWh
      accuracy_decimals: 3
    energy_3:
      name: "Channel 3 Energy"
      unit_of_measurement: kWh
      accuracy_decimals: 3
    energy_4:
      name: "Channel 4 Energy"
      unit_of_measurement: kWh
      accuracy_decimals: 3
    energy_5:
      name: "Channel 5 Energy"
      unit_of_measurement: kWh
      accuracy_decimals: 3
    energy_6:
      name: "Channel 6 Energy"
      unit_of_measurement: kWh
      accuracy_decimals: 3
    
    # 总电能
    energy_sum:
      name: "Total Energy"
      unit_of_measurement: kWh
      accuracy_decimals: 3
    
    # 频率
    frequency:
      name: "Line Frequency"
      unit_of_measurement: Hz
      accuracy_decimals: 1
    
    # 温度
    temperature:
      name: "BL0906 Temperature"
      unit_of_measurement: °C
      accuracy_decimals: 1 