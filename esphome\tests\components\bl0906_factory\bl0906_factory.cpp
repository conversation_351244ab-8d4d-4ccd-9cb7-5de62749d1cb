#include "bl0906_factory.h"
#include "bl0906_number.h"

#include <map>
#include <string>
#include "esphome/core/hal.h"
#include "esphome/core/application.h"
#include "esphome/core/time.h"

/*
 * 重要说明：
 * 此组件继承了uart::UARTDevice，因此不需要使用parent_指针。
 * 在ESPHome中，如果组件继承了UARTDevice，它已经可以直接使用this->write_byte()等方法进行通信。
 * 因此，不需要调用set_parent方法或使用parent_指针。
 * 本次修改将所有使用parent_指针的地方改为使用继承的UART方法（this->write_byte等）
 */

namespace esphome {
namespace bl0906_factory {

static const char *const FACTORY_TAG = "bl0906_factory";  // 修改日志标签
const char *const BL0906Factory::BL0906_FACTORY_ID = "bl0906_factory";
// 定义静态实例指针 - 修改：指向 BL0906Factory 类型
BL0906Factory *BL0906Factory::bl0906_instance = nullptr;

// 用户寄存器可操作/只读指令
uint8_t USR_WRPROT_Witable[6]={0xCA, 0x9E, 0x55, 0x55, 0x00, 0xB7};  //用户寄存器可操作指令
uint8_t USR_WRPROT_Onlyread[6]={0xCA, 0x9E, 0x00, 0x00, 0x00, 0x61};  //用户寄存器只读指令

// 添加校验和函数定义
uint8_t bl0906_checksum(const uint8_t address, const DataPacket *data) {
  uint8_t sum = address;
  sum += data->l;
  sum += data->m;
  sum += data->h;
  return sum ^ 0xFF;
}
BL0906Factory::BL0906Factory() {}
// 现代化状态机 - 更简洁高效
void BL0906Factory::loop() {
  while (this->available())
    this->flush();

  switch (this->current_state_) {
    case State::IDLE:
      this->current_channel_ = 0;
      this->current_state_ = State::READ_BASIC_SENSORS;
      break;

    case State::READ_BASIC_SENSORS:
      this->read_basic_sensors();
      this->current_state_ = State::READ_CHANNEL_DATA;
      break;

    case State::READ_CHANNEL_DATA:
      if (this->current_channel_ < CHANNEL_COUNT) {
        this->read_channel_data(this->current_channel_);
        this->current_channel_++;
      } else {
        this->current_state_ = State::READ_TOTAL_DATA;
      }
      break;

    case State::READ_TOTAL_DATA:
      this->read_data_(BL0906_WATT_SUM, Kp_sum, this->power_sum_sensor_);
      this->read_data_(BL0906_CF_SUM_CNT, Ke_sum, this->energy_sum_sensor_);
      this->current_state_ = State::HANDLE_ACTIONS;
      break;

    case State::HANDLE_ACTIONS:
      this->handle_actions_();
      // this->current_state_ = State::IDLE;  // 循环回到开始
      break;
  }
}

// 实现新的数据读取方法
void BL0906Factory::read_basic_sensors() {
  this->read_data_(BL0906_TEMPERATURE, TREF, this->temperature_sensor_);
  this->read_data_(BL0906_FREQUENCY, FREF, this->frequency_sensor_);
  this->read_data_(BL0906_V_RMS, Kv, this->voltage_sensor_);
}

void BL0906Factory::read_channel_data(int channel) {
  if (!is_valid_channel(channel)) {
    ESP_LOGW(FACTORY_TAG, "无效通道: %d", channel);
    return;
  }

  this->read_data_(BL0906_I_RMS[channel], Ki, this->current_sensors_[channel]);
  this->read_data_(BL0906_WATT[channel], Kp, this->power_sensors_[channel]);
  this->read_data_(BL0906_CF_CNT[channel], Ke, this->energy_sensors_[channel]);
}

// 实现update方法，仅重置状态机到第一个状态
void BL0906Factory::update() {
  this->current_state_ = State::IDLE;
}

size_t BL0906Factory::enqueue_action_(ActionCallbackFuncPtr function) {
  this->action_queue_.push_back(function);
  return this->action_queue_.size();
}

void BL0906Factory::handle_actions_() {
  if (this->action_queue_.empty()) {
    return;
  }
  ActionCallbackFuncPtr ptr_func = nullptr;
  for (int i = 0; i < this->action_queue_.size(); i++) {
    ptr_func = this->action_queue_[i];
    if (ptr_func) {
      ESP_LOGI(FACTORY_TAG, "HandleActionCallback[%d]...", i);
      (this->*ptr_func)();
    }
  }

  while (this->available()) {
    this->read();
  }

  this->action_queue_.clear();
}

// 现代化校准寄存器读取方法
void BL0906Factory::read_calib_register(CalibNumberType type, int channel) {
  uint8_t reg_addr = get_register_address(static_cast<CalibRegType>(type), channel);
  if (reg_addr == 0) {
    ESP_LOGW(FACTORY_TAG, "无效的校准寄存器类型或通道");
    return;
  }

  int32_t value = read_register_value(reg_addr);
  ESP_LOGD(FACTORY_TAG, "读取校准寄存器 0x%02X: %d", reg_addr, value);

  // 更新对应的Number组件
  number::Number* num = get_calib_number(type, channel);
  if (num) {
    num->publish_state(value);
  }
}

uint32_t BL0906Factory::to_uint32_t(ube24_t input) { return input.h << 16 | input.m << 8 | input.l; }

int32_t BL0906Factory::to_int32_t(sbe24_t input) { return input.h << 16 | input.m << 8 | input.l; }

// 添加方法实现
bool BL0906Factory::wait_until_available(size_t len, uint32_t timeout_ms) {
  const uint32_t start = esphome::millis();
  ESP_LOGV(FACTORY_TAG, "等待数据可用，需要%d字节，当前可用%d字节，超时%dms",
           len, this->available(), timeout_ms);

  while (this->available() < len) {
    if (esphome::millis() - start > timeout_ms) {
      ESP_LOGW(FACTORY_TAG, "等待数据超时，期望%d字节，实际可用:%d字节", len, this->available());
      return false;
    }

    // 更频繁地让出CPU时间片和喂狗
    yield();

    // 每5ms记录一次等待状态，方便诊断问题
    if (esphome::millis() % 5 == 0) {
      ESP_LOGV(FACTORY_TAG, "等待中...已等待%dms，需要%d字节，当前可用%d字节",
              (esphome::millis() - start), len, this->available());
    }

    // 短暂延时，减轻CPU负担
    delay(1);
  }

  ESP_LOGV(FACTORY_TAG, "数据可用，需要%d字节，当前可用%d字节，等待了%dms",
           len, this->available(), (esphome::millis() - start));
  return true;
}

// 实现BL0906Factory类的refresh_all_calib_numbers方法
void BL0906Factory::refresh_all_calib_numbers() {
  ESP_LOGI(FACTORY_TAG, "刷新所有校准数字组件...");

  for (auto *number : calib_numbers_) {
    if (number != nullptr) {
      number->update_from_register();
      ESP_LOGD(FACTORY_TAG, "Refreshing number instance: %p", number);
      // 防止看门狗复位
      arch_feed_wdt();
    }
    else
      ESP_LOGD(FACTORY_TAG, "指针为空");
  }

  ESP_LOGI(FACTORY_TAG, "所有校准数字组件刷新完成");
}

// 清空接收缓冲区的帮助函数
void BL0906Factory::flush_rx_buffer() {
  // 循环读取缓冲区中的所有字节来清空缓冲区
  while (this->available()) {
    this->read();
  }
  ESP_LOGV(FACTORY_TAG, "已清空接收缓冲区");
}

// 添加一个辅助函数用于发送读命令并接收数据包
bool BL0906Factory::send_read_command_and_receive(uint8_t address, DataPacket &data) {
  // 发送读命令
  this->write_byte(BL0906_READ_COMMAND);  // 读命令(0x35)
  this->write_byte(address);              // 寄存器地址
  this->flush(); // 确保命令已发送

  // 等待响应数据（3字节数据 + 1字节校验和 = sizeof(data)）
  if (!wait_until_available(sizeof(data), 100)) {  // 100ms超时，等待所有字节均可用
    ESP_LOGE(FACTORY_TAG, "等待读取寄存器响应数据超时 (地址: 0x%02X, 需要 %d 字节)", address, (int)sizeof(data));
    return false;
  }

  // 读取响应数据. 此时字节应该已经可用。
  if (!this->read_array((uint8_t *)&data, sizeof(data))) { // 使用 UARTDevice::read_array
    ESP_LOGE(FACTORY_TAG, "使用 UARTDevice::read_array 读取寄存器数据包失败 (地址: 0x%02X, 需要 %d 字节)", address, (int)sizeof(data));
    return false;
  }

  // 验证校验和
  const uint8_t expected_checksum = bl0906_checksum(address, &data);
  if (data.checksum != expected_checksum) {
    ESP_LOGE(FACTORY_TAG, "寄存器校验异常 地址:0x%02X 计算:%02X 接收:%02X",
           address, expected_checksum, data.checksum);
    return false;
  }

  return true;
}

// 修改read_register_value方法使用新的辅助函数
int32_t BL0906Factory::read_register_value(uint8_t address) {
  ESP_LOGI(FACTORY_TAG, "读取寄存器0x%02X的值", address);

  // 清空接收缓冲区
  flush_rx_buffer();

  // 发送读命令并接收数据
  DataPacket data;
  if (!send_read_command_and_receive(address, data)) {
    ESP_LOGE(FACTORY_TAG, "读取寄存器0x%02X失败", address);
    return 0;
  }

  // 处理数据
  int32_t raw;

  // 对于16位寄存器，特殊处理
  if (is_16bit_register(address)) {
    // 对于16位寄存器，使用data.m和data.l组成16位值，并正确扩展符号位
    uint16_t raw_16bit = ((uint16_t)data.m << 8) | data.l;

    // 使用移位操作处理符号位扩展
    raw = ((int32_t)(raw_16bit << 16)) >> 16;

    ESP_LOGV(FACTORY_TAG, "16位寄存器读取: 0x%02X -> 0x%04X = %d (有符号值)",
             address, raw_16bit, raw);
  } else {
    // 对于24位寄存器，组合三个字节并处理符号位扩展
    uint32_t raw_24bit = ((uint32_t)data.h << 16) | (data.m << 8) | data.l;

    // 使用移位操作处理符号位扩展
    raw = ((int32_t)(raw_24bit << 8)) >> 8;

    ESP_LOGV(FACTORY_TAG, "标准寄存器读取: 0x%02X -> 0x%06X = %d (有符号值)",
             address, raw_24bit, raw);
  }

  return raw;
}

// 添加一个辅助函数用于发送写命令和数据
bool BL0906Factory::send_write_command(uint8_t reg, uint8_t l, uint8_t m, uint8_t h) {
  // 计算校验和（寄存器地址、数据和h字节）
  uint8_t sum = reg + l + m + h;
  uint8_t checksum = ~sum;  // 取反

  // 构建发送的字节数组
  uint8_t buffer[6];
  buffer[0] = BL0906_WRITE_COMMAND;
  buffer[1] = reg;
  buffer[2] = l;
  buffer[3] = m;
  buffer[4] = h;
  buffer[5] = checksum;

  // 发送写命令
  this->write_array(buffer, sizeof(buffer)); // 使用 UARTDevice 的 write_array
  this->flush();

  ESP_LOGV(FACTORY_TAG, "发送写命令: 0x%02X 地址:0x%02X 数据:0x%02X%02X%02X 校验和:0x%02X",
           BL0906_WRITE_COMMAND, reg, h, m, l, checksum);

  return true;
}

// 修改write_register_value方法使用新的辅助函数
bool BL0906Factory::write_register_value(uint8_t reg, int16_t value) {
  ESP_LOGI(FACTORY_TAG, "正在写入寄存器 0x%02X 值: %d", reg, value);

  // 在写入前解除写保护
  if (!this->turn_off_write_protect()) {
    ESP_LOGE(FACTORY_TAG, "无法解除写保护，写入寄存器失败");
    return false;
  }

  ESP_LOGI(FACTORY_TAG, "写保护已解除，继续写入寄存器 0x%02X", reg);

  // 清空接收缓冲区
  flush_rx_buffer();

  // 准备写入数据
  uint8_t l, m, h;

  // 根据寄存器类型决定如何准备数据
  if (is_16bit_register(reg)) {
    // 对于16位寄存器（CHGN、CHOS等）
    l = value & 0xFF;          // 低字节
    m = (value >> 8) & 0xFF;   // 中字节(符号位所在的字节)
    h = 0;                     // 高字节为0

    ESP_LOGV(FACTORY_TAG, "16位寄存器写入: 0x%02X <- 0x%02X%02X (值: %d)",
             reg, m, l, value);
  } else {
    // 对于24位寄存器（RMSOS等）
    // 使用移位操作来扩展16位有符号数到24位
    // (value << 8) >> 8 会保持符号位扩展，对于负数会得到低24位全为1的结果
    int32_t value_24bit = ((int32_t)(value << 8)) >> 8;

    // 确保只使用低24位
    value_24bit &= 0x00FFFFFF;

    ESP_LOGV(FACTORY_TAG, "24位寄存器写入: 0x%02X <- 0x%06X (值: %d)",
             reg, value_24bit, value);

    // 将24位值拆分为三个字节
    l = value_24bit & 0xFF;          // 低字节
    m = (value_24bit >> 8) & 0xFF;   // 中字节
    h = (value_24bit >> 16) & 0xFF;  // 高字节(符号位所在的字节)
  }

  // 发送写命令和数据
  if (!send_write_command(reg, l, m, h)) {
    ESP_LOGE(FACTORY_TAG, "发送写命令失败，寄存器:0x%02X", reg);
    return false;
  }

  // 延时等待写入完成
  delay(5);

  // 使用read_register_value读回并验证写入的值
  int32_t read_value = read_register_value(reg);

  // 比较读回值与期望值
  if (read_value == value) {
    ESP_LOGI(FACTORY_TAG, "寄存器 0x%02X 写入成功，值: %d", reg, value);
    return true;
  } else {
    ESP_LOGE(FACTORY_TAG, "寄存器 0x%02X 写入验证失败，写入值=%d，读回值=%d",
             reg, value, read_value);
    return false;
  }
}

// 修改turn_off_write_protect方法，集成写保护状态检查功能
bool BL0906Factory::turn_off_write_protect() {
  ESP_LOGI(FACTORY_TAG, "开始执行关闭写保护操作");

  // 清空接收缓冲区
  flush_rx_buffer();

  // 写保护状态寄存器地址
  const uint8_t WR_PROTECT_REG = 0x9E;

  // 检查当前写保护状态
  DataPacket data;
  if (!send_read_command_and_receive(WR_PROTECT_REG, data)) {
    ESP_LOGE(FACTORY_TAG, "读取写保护状态失败");
    return false;
  }

  // 分析写保护状态
  bool is_unlocked = !(data.l == 0x00);  // 根据数据手册判断写保护状态位

  if (is_unlocked) {
    ESP_LOGI(FACTORY_TAG, "写保护已经是关闭状态，无需操作");
    return true;
  }

  ESP_LOGI(FACTORY_TAG, "开始解除寄存器写保护");
  bool success = false;

  // 重复尝试解锁，最多3次
  for (int attempt = 0; attempt < 3; attempt++) {
    // 写入解锁序列
    this->write_array(USR_WRPROT_Witable, sizeof(USR_WRPROT_Witable)); // 使用 UARTDevice 的 write_array
    this->flush();

    // 延时以确保命令被处理
    delay(10);

    // 清空接收缓冲区
    flush_rx_buffer();

    // 验证写保护是否成功解除
    if (!send_read_command_and_receive(WR_PROTECT_REG, data)) {
      ESP_LOGE(FACTORY_TAG, "读取写保护状态失败 (尝试 %d)", attempt + 1);
      continue;
    }

    is_unlocked = !(data.l == 0x00);
    if (is_unlocked) {
      ESP_LOGI(FACTORY_TAG, "写保护已成功解除 (尝试 %d)", attempt + 1);
      success = true;
      break;
    }

    ESP_LOGW(FACTORY_TAG, "写保护解锁尝试 %d 失败，重试...", attempt + 1);
    delay(50); // 在重试前等待更长时间
  }

  if (!success) {
    ESP_LOGE(FACTORY_TAG, "关闭写保护操作失败");
    return false;
  }

  ESP_LOGI(FACTORY_TAG, "写保护关闭操作成功确认");
  return true;
}

// 添加一个辅助函数，判断寄存器是否为16位寄存器
bool BL0906Factory::is_16bit_register(uint8_t address) {
  // CHGN系列: 0xA1-0xA8, 0xAA
  // CHOS系列: 0xAC-0xAF, 0xB2-0xB3, 0xB5
  // RMSGN系列: 0x6D-0x74
  // WATTGN系列: 0xB7-0xBE
  // WATTOS系列: 0xC1-0xC8
  return (address >= 0xA1 && address <= 0xA8) ||  // CHGN 1-6
         (address == 0xAA) ||                     // CHGN_V
         (address >= 0xAC && address <= 0xAF) ||  // CHOS 1-4
         (address >= 0xB2 && address <= 0xB3) ||  // CHOS 5-6
         (address == 0xB5) ||                     // CHOS_V
         (address >= 0x6D && address <= 0x74) ||  // RMSGN 1-6
         (address >= 0xB7 && address <= 0xBE) ||  // WATTGN 1-6
         (address >= 0xC1 && address <= 0xC8);    // WATTOS 1-6
}

// 修改read_data_方法使用新的辅助函数
void BL0906Factory::read_data_(uint8_t address, float reference, sensor::Sensor *sensor) {
  if (!sensor) {
    ESP_LOGW(FACTORY_TAG, "传感器指针为空");
    return;
  }

  // 清空缓冲区
  flush_rx_buffer();

  // 发送读命令并接收数据
  DataPacket data;
  if (!send_read_command_and_receive(address, data)) {
    ESP_LOGE(FACTORY_TAG, "读取失败，地址:0x%02X", address);
    return;
  }

  int32_t raw = (data.h << 16) | (data.m << 8) | data.l;
  float value;

  switch (address) {
    case BL0906_FREQUENCY:
      value = (raw > 0) ? 10000000.0f / raw : 0;
      break;
    case BL0906_TEMPERATURE:
      value = (raw - 64) * 12.5f / 59.0f - 40.0f;
      break;
    default:
      value = raw / reference;
  }

  sensor->publish_state(value);
  ESP_LOGV(FACTORY_TAG, "地址=0x%02X, 原始值=%d, 计算值=%.2f", address, raw, value);
}

// 修改setup方法，简化初始化过程
void BL0906Factory::setup() {
  ESP_LOGD(FACTORY_TAG, "开始初始化BL0906...");
  // 初始化互斥锁
  this->init_mutex();

  // 清空缓冲区
  flush_rx_buffer();

  // 应用YAML配置中的校准值
  apply_calibration_values();

  // 读取校准寄存器
  refresh_all_calib_numbers();
  ESP_LOGI(FACTORY_TAG, "BL0906初始化完成，已注册 %d 个校准数字组件", calib_numbers_.size());
}

// 新增方法：应用校准值
void BL0906Factory::apply_calibration_values() {
  const size_t total_values = initial_calibration_values_.size();

  if (total_values == 0) {
    ESP_LOGI(FACTORY_TAG, "没有校准值需要应用");
    return;
  }

  ESP_LOGI(FACTORY_TAG, "开始应用 %zu 个校准值", total_values);

  // 解除写保护
  if (!this->turn_off_write_protect()) {
    ESP_LOGE(FACTORY_TAG, "无法解除写保护，校准值未应用");
    return;
  }

  // 批量写入所有校准值
  int success_count = 0;
  size_t current = 0;

  for (const auto& [reg_addr, value] : initial_calibration_values_) {
    current++;

    // 每5个值或最后一个值显示进度
    if (current % 5 == 0 || current == total_values) {
      ESP_LOGI(FACTORY_TAG, "应用进度: %zu/%zu (%.1f%%)",
               current, total_values, current * 100.0f / total_values);
    }

    ESP_LOGD(FACTORY_TAG, "写入寄存器 0x%02X 值: %d", reg_addr, value);

    if (this->write_register_value(reg_addr, value)) {
      success_count++;

      // 防止看门狗复位
      arch_feed_wdt();

      // 短暂延时，避免连续写入过快
      delay(2);
    }
  }

  ESP_LOGI(FACTORY_TAG, "校准值应用完成：%d 成功，%d 失败",
          success_count, (int)(total_values - success_count));
}

// 实现register_calib_number方法
void BL0906Factory::register_calib_number(BL0906Number *number) {
  number->set_parent(this);  // 设置父指针
  calib_numbers_.push_back(number);
}

}  // namespace bl0906_factory
}  // namespace esphome
