#pragma once

namespace esphome {
namespace bl0906_factory {

// 转换系数和校正常量
// static constexpr int Rt = 2000;
// static constexpr float K_p_sum = 16*1.097*1.097*(20000+20000+20000+20000+20000)/(40.41259*((5.1+5.1)*1000/Rt)*1*100*1*1000);//总功率转换*16
// static constexpr float K_e_sum = 16*4194304*0.032768*16/(3600000*16*(40.4125*((5.1+5.1)*1000/Rt)*1*100*1*1000/(1.097*1.097*(20000+20000+20000+20000+20000))));//总电量转换*16
// static constexpr float FREF = 10000000;//频率转换
// static constexpr float TREF = 12.5/59-40;//温度转换 内部温度=（TPS-64）*12.5/59-40 （℃）
// static constexpr float K_i = 1.097/(12875*1*(5.1+5.1)*1000/Rt); //电流值转换
// static constexpr float K_v = 1.097*(20000+20000+20000+20000+20000)/(13162*1*100*1000); //电压值转换
// static constexpr float K_p = 1.097*1.097*(20000+20000+20000+20000+20000)/(40.41259*((5.1+5.1)*1000/Rt)*1*100*1*1000); //功率值转换
// static constexpr float K_e = 4194304*0.032768*16/(3600000*16*(40.4125*((5.1+5.1)*1000/Rt)*1*100*1*1000/(1.097*1.097*(20000+20000+20000+20000+20000)))); //电量值转换

// BL0906 配置参数
#define Vref           1.097f      // 内部参考电压 (V)
#define Gain_V    1           // 电压通道增益 (1, 2, 8, 16)
#define Gain_I    1           // 电流通道增益 (1, 2, 8, 16)
#define Rf             1500.0f  // 分压上拉电阻 (kΩ)
#define Rv             1.0f     // 分压下拉电阻 (kΩ)
#define RL             5.1f        // 互感器副边负载电阻 (Ω)
#define Rt             2000.0f     // 互感器变比，如 2000:1
#define R46             100        // 电压采样电阻 (Ω)


static constexpr float Ki = (12875 * Gain_I * (RL + RL) * 1000 / Rt) / Vref;
static constexpr float Kv = (13162 * Gain_V * R46 * 1000) / (Vref * Rf);
static constexpr float Kp = (40.4125 * ((RL + RL) * 1000 / Rt) * R46 * 1000) /
           (pow(Vref, 2) * Rf);
static constexpr float Kp_sum = Kp/16 ;

static constexpr float Ke = (3600000.0f * 16 * Kp) / (4194304.0f * 0.032768f * 16);  // 单位：kWh/pulse
static constexpr float Ke_sum = Ke/16 ;
static constexpr float FREF = 1/10000000;//频率转换
static constexpr float TREF = 59-40/12.5;//温度转换 内部温度=（TPS-64）*12.5/59-40 （℃）
}  // namespace bl0906_factory
}  // namespace esphome