# BL0906 Factory 通道索引修复总结

## 🐛 问题描述

在彻底重构过程中，我将通道索引从用户期望的1-6改为了0-5，导致配置验证失败：
```
[current_6] is an invalid option for [sensor.bl0906_factory]. 
Did you mean [current_5], [current_4], [current_3]?
```

## 🔧 修复方案

### 1. 保持用户接口一致性

**用户配置层面**: 继续使用1-6通道编号
- `current_1`, `current_2`, ..., `current_6`
- `power_1`, `power_2`, ..., `power_6`
- `energy_1`, `energy_2`, ..., `energy_6`
- `chgn_decimal_1`, `chgn_decimal_2`, ..., `chgn_decimal_6`

**内部实现层面**: 使用0-5数组索引
- `current_sensors_[0]` 对应 `current_1`
- `current_sensors_[1]` 对应 `current_2`
- ...
- `current_sensors_[5]` 对应 `current_6`

### 2. 修复的文件和内容

#### sensor.py 修复
```python
# 修复前：生成 current_0 到 current_5
for i in range(CHANNEL_COUNT):
    key = f"{sensor_type}_{i}"
    SENSOR_CONFIGS[key] = {**template, "channel": i}

# 修复后：生成 current_1 到 current_6，内部映射到 0-5
for i in range(1, CHANNEL_COUNT + 1):
    key = f"{sensor_type}_{i}"
    SENSOR_CONFIGS[key] = {**template, "channel": i - 1}  # 内部使用0-5索引
```

#### number.py 修复
```python
# 修复前：生成 chgn_0 到 chgn_5
"channels": list(range(CHANNEL_COUNT))

# 修复后：生成 chgn_decimal_1 到 chgn_decimal_6
"channels": list(range(1, CHANNEL_COUNT + 1))

# 配置生成逻辑
if channel == -1:  # 电压通道
    key = f"{calib_type.lower()}_v_decimal"
    internal_channel = -1
else:
    key = f"{calib_type.lower()}_decimal_{channel}"
    internal_channel = channel - 1  # 内部使用0-5索引
```

#### 寄存器地址映射修复
```python
# 确保寄存器地址正确映射
if channel == -1:  # 电压通道
    reg_expression = f"esphome::bl0906_factory::{register_prefix}_V"
else:
    reg_expression = f"esphome::bl0906_factory::{register_prefix}_{channel + 1}"  # 寄存器使用1-6
```

### 3. 索引映射关系

| 用户配置 | 内部数组索引 | 寄存器编号 |
|---------|-------------|-----------|
| current_1 | 0 | BL0906_I_1_RMS |
| current_2 | 1 | BL0906_I_2_RMS |
| current_3 | 2 | BL0906_I_3_RMS |
| current_4 | 3 | BL0906_I_4_RMS |
| current_5 | 4 | BL0906_I_5_RMS |
| current_6 | 5 | BL0906_I_6_RMS |

| 用户配置 | 内部通道 | 寄存器编号 |
|---------|---------|-----------|
| chgn_decimal_1 | 0 | BL0906_CHGN_1 |
| chgn_decimal_2 | 1 | BL0906_CHGN_2 |
| chgn_decimal_3 | 2 | BL0906_CHGN_3 |
| chgn_decimal_4 | 3 | BL0906_CHGN_4 |
| chgn_decimal_5 | 4 | BL0906_CHGN_5 |
| chgn_decimal_6 | 5 | BL0906_CHGN_6 |
| chgn_v_decimal | -1 | BL0906_CHGN_V |

### 4. 验证配置

创建了测试配置文件 `test_config.yaml` 来验证修复：
- 包含所有传感器类型（current_1 到 current_6）
- 包含所有校准Number组件（chgn_decimal_1 到 chgn_decimal_6）
- 验证配置解析正确性

## ✅ 修复结果

1. **用户体验**: 保持原有的1-6通道命名约定
2. **内部一致性**: 使用0-5数组索引提高效率
3. **寄存器映射**: 正确映射到硬件寄存器地址
4. **配置验证**: 所有通道配置现在都能正确识别

## 🎯 关键改进

1. **双重索引系统**: 用户友好的1-6编号 + 内部高效的0-5索引
2. **自动映射**: 配置生成时自动处理索引转换
3. **类型安全**: 保持强类型检查和验证
4. **向前兼容**: 为未来可能的通道扩展做好准备

这次修复确保了重构后的代码既保持了现代化的内部架构，又维持了用户熟悉的配置接口。
