"""BL0906 Factory - 现代化电能计量组件"""
import esphome.codegen as cg
import esphome.config_validation as cv
from esphome.components import uart, number
from esphome.const import (
    CONF_ID,
    CONF_UPDATE_INTERVAL
)

DEPENDENCIES = ["uart"]
CODEOWNERS = ["@carrot8848"]
AUTO_LOAD = ["sensor", "number"]
MULTI_CONF = True

# 现代化命名空间定义
bl0906_factory_ns = cg.esphome_ns.namespace("bl0906_factory")
BL0906Factory = bl0906_factory_ns.class_("BL0906Factory", cg.PollingComponent, uart.UARTDevice)
BL0906Number = bl0906_factory_ns.class_("BL0906Number", number.Number, cg.Component)

# 现代化枚举定义
CalibRegType = bl0906_factory_ns.enum("CalibRegType")
SensorType = bl0906_factory_ns.enum("SensorType", is_class=True)
CalibNumberType = bl0906_factory_ns.enum("CalibNumberType", is_class=True)

# 现代化常量定义
CONF_BL0906_FACTORY_ID = "bl0906_factory_id"
CONF_CALIBRATION = "calibration"

# 简化的校准配置 - 移除复杂的嵌套结构
CALIBRATION_SCHEMA = cv.Schema({
    cv.Optional("enabled", default=True): cv.boolean,
    cv.Optional("auto_apply", default=True): cv.boolean,
})

# 组件配置模式
CONFIG_SCHEMA = cv.Schema({
    cv.GenerateID(): cv.declare_id(BL0906Factory),
    cv.Optional(CONF_UPDATE_INTERVAL, default="60s"): cv.update_interval,
    cv.Optional(CONF_CALIBRATION): CALIBRATION_SCHEMA,
}).extend(cv.polling_component_schema("60s")).extend(uart.UART_DEVICE_SCHEMA)

FINAL_VALIDATE_SCHEMA = uart.final_validate_device_schema("bl0906_factory")

async def to_code(config):
    var = cg.new_Pvariable(config[CONF_ID])
    await cg.register_component(var, config)
    await uart.register_uart_device(var, config)

    # 添加现代化组件标识
    cg.add_define("USE_BL0906_FACTORY")

    # 简化的校准配置处理
    if CONF_CALIBRATION in config:
        calib_config = config[CONF_CALIBRATION]
        if calib_config.get("enabled", True):
            cg.add_define("BL0906_CALIBRATION_ENABLED")
        if calib_config.get("auto_apply", True):
            cg.add_define("BL0906_AUTO_APPLY_CALIBRATION")

# 现代化平台模式定义
PLATFORM_SCHEMA = cv.Schema({
    cv.GenerateID(CONF_BL0906_FACTORY_ID): cv.use_id(BL0906Factory),
})
