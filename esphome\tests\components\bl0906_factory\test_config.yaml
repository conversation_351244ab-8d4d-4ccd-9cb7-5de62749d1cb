# BL0906 Factory 组件测试配置
esphome:
  name: bl0906-test
  platform: ESP32
  board: esp32dev

wifi:
  ssid: "test"
  password: "test"

logger:

api:

ota:

uart:
  id: uart_bus
  tx_pin: GPIO1
  rx_pin: GPIO3
  baud_rate: 9600

bl0906_factory:
  id: bl0906_component
  uart_id: uart_bus
  update_interval: 5s

sensor:
  # 基础传感器
  - platform: bl0906_factory
    bl0906_factory_id: bl0906_component
    voltage:
      name: "Voltage"
    frequency:
      name: "Frequency"
    temperature:
      name: "Temperature"
    
    # 通道传感器 (1-6)
    current_1:
      name: "Current 1"
    current_2:
      name: "Current 2"
    current_3:
      name: "Current 3"
    current_4:
      name: "Current 4"
    current_5:
      name: "Current 5"
    current_6:
      name: "Current 6"
    
    power_1:
      name: "Power 1"
    power_2:
      name: "Power 2"
    power_3:
      name: "Power 3"
    power_4:
      name: "Power 4"
    power_5:
      name: "Power 5"
    power_6:
      name: "Power 6"
    
    energy_1:
      name: "Energy 1"
    energy_2:
      name: "Energy 2"
    energy_3:
      name: "Energy 3"
    energy_4:
      name: "Energy 4"
    energy_5:
      name: "Energy 5"
    energy_6:
      name: "Energy 6"
    
    # 总和传感器
    power_sum:
      name: "Total Power"
    energy_sum:
      name: "Total Energy"

number:
  # 校准Number组件
  - platform: bl0906_factory
    bl0906_factory_id: bl0906_component
    
    # CHGN 电流增益
    chgn_decimal_1:
      name: "Current Gain 1"
    chgn_decimal_2:
      name: "Current Gain 2"
    chgn_decimal_3:
      name: "Current Gain 3"
    chgn_decimal_4:
      name: "Current Gain 4"
    chgn_decimal_5:
      name: "Current Gain 5"
    chgn_decimal_6:
      name: "Current Gain 6"
    chgn_v_decimal:
      name: "Voltage Gain"
    
    # CHOS 电流偏置
    chos_decimal_1:
      name: "Current Offset 1"
    chos_decimal_2:
      name: "Current Offset 2"
    chos_decimal_3:
      name: "Current Offset 3"
    chos_decimal_4:
      name: "Current Offset 4"
    chos_decimal_5:
      name: "Current Offset 5"
    chos_decimal_6:
      name: "Current Offset 6"
    chos_v_decimal:
      name: "Voltage Offset"
