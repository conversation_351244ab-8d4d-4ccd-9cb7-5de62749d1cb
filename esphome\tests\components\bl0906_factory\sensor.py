import esphome.codegen as cg
import esphome.config_validation as cv
from esphome.components import sensor
from esphome.const import (
    CONF_ID, CONF_FREQUENCY, CONF_TEMPERATURE, CONF_VOLTAGE,
    CONF_CURRENT, CONF_POWER, CONF_ENERGY,
    DEVICE_CLASS_CURRENT, DEVICE_CLASS_ENERGY, DEVICE_CLASS_POWER,
    DEVICE_CLASS_VOLTAGE, DEVICE_CLASS_TEMPERATURE, DEVICE_CLASS_FREQUENCY,
    STATE_CLASS_MEASUREMENT, STATE_CLASS_TOTAL_INCREASING,
    UNIT_VOLT, UNIT_AMPERE, UNIT_WATT, UNIT_KILOWATT_HOURS,
    UNIT_HERTZ, UNIT_CELSIUS
)
from . import BL0906Factory, CONF_BL0906_FACTORY_ID

DEPENDENCIES = ["bl0906_factory"]

# 现代化传感器配置 - 使用数据驱动方式
CHANNEL_COUNT = 6

# 传感器类型枚举映射
SENSOR_TYPES = {
    "VOLTAGE": 0,
    "FREQUENCY": 1,
    "TEMPERATURE": 2,
    "CURRENT": 3,
    "POWER": 4,
    "ENERGY": 5,
    "POWER_SUM": 6,
    "ENERGY_SUM": 7
}

# 现代化传感器配置 - 数据驱动
SENSOR_CONFIGS = {
    # 基础传感器
    CONF_FREQUENCY: {
        "type": "FREQUENCY",
        "unit": UNIT_HERTZ,
        "accuracy": 1,
        "device_class": DEVICE_CLASS_FREQUENCY,
        "state_class": STATE_CLASS_MEASUREMENT,
    },
    CONF_TEMPERATURE: {
        "type": "TEMPERATURE",
        "unit": UNIT_CELSIUS,
        "accuracy": 1,
        "device_class": DEVICE_CLASS_TEMPERATURE,
        "state_class": STATE_CLASS_MEASUREMENT,
    },
    CONF_VOLTAGE: {
        "type": "VOLTAGE",
        "unit": UNIT_VOLT,
        "accuracy": 1,
        "device_class": DEVICE_CLASS_VOLTAGE,
        "state_class": STATE_CLASS_MEASUREMENT,
    },
    # 总和传感器
    "power_sum": {
        "type": "POWER_SUM",
        "unit": UNIT_WATT,
        "accuracy": 1,
        "device_class": DEVICE_CLASS_POWER,
        "state_class": STATE_CLASS_MEASUREMENT,
    },
    "energy_sum": {
        "type": "ENERGY_SUM",
        "unit": UNIT_KILOWATT_HOURS,
        "accuracy": 3,
        "device_class": DEVICE_CLASS_ENERGY,
        "state_class": STATE_CLASS_TOTAL_INCREASING,
    },
}

# 通道传感器模板
CHANNEL_SENSOR_TEMPLATES = {
    "current": {
        "type": "CURRENT",
        "unit": UNIT_AMPERE,
        "accuracy": 3,
        "device_class": DEVICE_CLASS_CURRENT,
        "state_class": STATE_CLASS_MEASUREMENT,
    },
    "power": {
        "type": "POWER",
        "unit": UNIT_WATT,
        "accuracy": 1,
        "device_class": DEVICE_CLASS_POWER,
        "state_class": STATE_CLASS_MEASUREMENT,
    },
    "energy": {
        "type": "ENERGY",
        "unit": UNIT_KILOWATT_HOURS,
        "accuracy": 3,
        "device_class": DEVICE_CLASS_ENERGY,
        "state_class": STATE_CLASS_TOTAL_INCREASING,
    },
}

# 动态生成通道传感器配置 (通道1-6)
for sensor_type, template in CHANNEL_SENSOR_TEMPLATES.items():
    for i in range(1, CHANNEL_COUNT + 1):
        key = f"{sensor_type}_{i}"
        SENSOR_CONFIGS[key] = {**template, "channel": i - 1}  # 内部使用0-5索引

# 现代化配置模式构建
def build_config_schema():
    """动态构建配置模式"""
    schema_dict = {cv.GenerateID(CONF_BL0906_FACTORY_ID): cv.use_id(BL0906Factory)}

    # 添加所有传感器配置
    for key, config in SENSOR_CONFIGS.items():
        schema_dict[cv.Optional(key)] = sensor.sensor_schema(
            unit_of_measurement=config["unit"],
            accuracy_decimals=config["accuracy"],
            device_class=config["device_class"],
            state_class=config["state_class"],
        )

    return cv.Schema(schema_dict)

CONFIG_SCHEMA = build_config_schema()

async def to_code(config):
    var = await cg.get_variable(config[CONF_BL0906_FACTORY_ID])

    # 现代化传感器注册 - 数据驱动
    for sensor_key, sensor_config in SENSOR_CONFIGS.items():
        if sensor_key in config:
            sens = await sensor.new_sensor(config[sensor_key])

            # 获取传感器类型和通道
            sensor_type = sensor_config["type"]
            channel = sensor_config.get("channel", 0)

            # 使用统一的set_sensor接口
            sensor_type_enum = cg.RawExpression(f"esphome::bl0906_factory::BL0906Factory::SensorType::{sensor_type}")
            cg.add(var.set_sensor(sensor_type_enum, sens, channel))
